<?php namespace C<PERSON><PERSON>ck\EloquentSluggable\Tests\Models;

use C<PERSON>brock\EloquentSluggable\Sluggable;
use Illuminate\Database\Eloquent\Model;

/**
 * Class Post
 *
 * @package Cviebrock\EloquentSluggable\Tests\Models
 *
 * @property integer id
 * @property string title
 * @property string|null subtitle
 * @property string|null slug
 * @property string|null dummy
 * @property integer author_id
 */
class Post extends Model
{

    use Sluggable;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'posts';

    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = false;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = ['title', 'subtitle', 'slug', 'dummy', 'author_id'];

    /**
     * Convert the model to its string representation.
     *
     * @return string
     */
    public function __toString()
    {
        return $this->title;
    }

    /**
     * Return the sluggable configuration array for this model.
     *
     * @return array
     */
    public function sluggable(): array
    {
        return [
            'slug' => [
                'source' => 'title'
            ]
        ];
    }
}
