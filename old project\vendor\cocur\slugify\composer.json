{"name": "cocur/slugify", "type": "library", "description": "Converts a string into a slug.", "keywords": ["slug", "slugify"], "license": "MIT", "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://florian.ec"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "require": {"php": "~8.0.0 || ~8.1.0 || ~8.2.0 || ~8.3.0 || ~8.4.0", "ext-mbstring": "*"}, "conflict": {"symfony/config": "<3.4 || >=4,<4.3", "symfony/dependency-injection": "<3.4 || >=4,<4.3", "symfony/http-kernel": "<3.4 || >=4,<4.3", "twig/twig": "<2.12.1"}, "require-dev": {"laravel/framework": "^5.0|^6.0|^7.0|^8.0", "latte/latte": "~2.2", "league/container": "^2.2.0", "mikey179/vfsstream": "~1.6.8", "mockery/mockery": "^1.3", "nette/di": "~2.4", "pimple/pimple": "~1.1", "plumphp/plum": "~0.1", "symfony/config": "^3.4 || ^4.3 || ^5.0 || ^6.0", "symfony/dependency-injection": "^3.4 || ^4.3 || ^5.0 || ^6.0", "symfony/http-kernel": "^3.4 || ^4.3 || ^5.0 || ^6.0", "symfony/phpunit-bridge": "^5.4 || ^6.0", "twig/twig": "^2.12.1 || ~3.0", "zendframework/zend-modulemanager": "~2.2", "zendframework/zend-servicemanager": "~2.2", "zendframework/zend-view": "~2.2"}, "autoload": {"psr-4": {"Cocur\\Slugify\\": "src"}}, "autoload-dev": {"psr-4": {"Cocur\\Slugify\\Tests\\": "tests"}}}