{"name": "barryvdh/laravel-debugbar", "description": "PHP Debugbar integration for Laravel", "keywords": ["laravel", "debugbar", "profiler", "debug", "webprofiler", "dev"], "license": "MIT", "authors": [{"name": "Barry vd. Heuvel", "email": "<EMAIL>"}], "require": {"php": "^8.1", "php-debugbar/php-debugbar": "~2.1.1", "illuminate/routing": "^9|^10|^11|^12", "illuminate/session": "^9|^10|^11|^12", "illuminate/support": "^9|^10|^11|^12", "symfony/finder": "^6|^7"}, "require-dev": {"mockery/mockery": "^1.3.3", "orchestra/testbench-dusk": "^7|^8|^9|^10", "phpunit/phpunit": "^9.5.10|^10|^11", "squizlabs/php_codesniffer": "^3.5"}, "autoload": {"psr-4": {"Barryvdh\\Debugbar\\": "src/"}, "files": ["src/helpers.php"]}, "autoload-dev": {"psr-4": {"Barryvdh\\Debugbar\\Tests\\": "tests"}}, "minimum-stability": "dev", "prefer-stable": true, "extra": {"branch-alias": {"dev-master": "3.15-dev"}, "laravel": {"providers": ["Barryvdh\\Debugbar\\ServiceProvider"], "aliases": {"Debugbar": "Barryvdh\\Debugbar\\Facades\\Debugbar"}}}, "scripts": {"check-style": "phpcs", "fix-style": "phpcbf", "test": "phpunit"}}