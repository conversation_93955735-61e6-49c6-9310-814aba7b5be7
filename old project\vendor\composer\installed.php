<?php return array(
    'root' => array(
        'name' => 'laravel/laravel',
        'pretty_version' => 'dev-master',
        'version' => 'dev-master',
        'reference' => 'd2570354de71ac4e9eafd9008435219168dfa3b8',
        'type' => 'project',
        'install_path' => __DIR__ . '/../../',
        'aliases' => array(),
        'dev' => true,
    ),
    'versions' => array(
        'anhskohbo/no-captcha' => array(
            'pretty_version' => '3.6.0',
            'version' => '3.6.0.0',
            'reference' => '6f129419a7f0d0a1ed9849fdaaed34e6d83a03cc',
            'type' => 'library',
            'install_path' => __DIR__ . '/../anhskohbo/no-captcha',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'artesaos/seotools' => array(
            'pretty_version' => 'v1.3.1',
            'version' => '1.3.1.0',
            'reference' => '44920c7408c927f63c505aadcf35037c55c0a818',
            'type' => 'library',
            'install_path' => __DIR__ . '/../artesaos/seotools',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'artisync/image' => array(
            'pretty_version' => 'v9.3.9',
            'version' => '9.3.9.0',
            'reference' => '7e4e8eb67219d854f1a94a4760f6843e2c59c11f',
            'type' => 'library',
            'install_path' => __DIR__ . '/../artisync/image',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'barryvdh/laravel-debugbar' => array(
            'pretty_version' => 'v3.15.0',
            'version' => '3.15.0.0',
            'reference' => '77cca4a1162d45e1fe64e7a71b4a3031656b6c86',
            'type' => 'library',
            'install_path' => __DIR__ . '/../barryvdh/laravel-debugbar',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'brick/math' => array(
            'pretty_version' => '0.12.1',
            'version' => '0.12.1.0',
            'reference' => 'f510c0a40911935b77b86859eb5223d58d660df1',
            'type' => 'library',
            'install_path' => __DIR__ . '/../brick/math',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'carbonphp/carbon-doctrine-types' => array(
            'pretty_version' => '3.2.0',
            'version' => '3.2.0.0',
            'reference' => '18ba5ddfec8976260ead6e866180bd5d2f71aa1d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../carbonphp/carbon-doctrine-types',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'cocur/slugify' => array(
            'pretty_version' => 'v4.6.0',
            'version' => '4.6.0.0',
            'reference' => '1d674022e9cbefa80b4f51aa3e2375b6e3c14fdb',
            'type' => 'library',
            'install_path' => __DIR__ . '/../cocur/slugify',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'cordoval/hamcrest-php' => array(
            'dev_requirement' => true,
            'replaced' => array(
                0 => '*',
            ),
        ),
        'cviebrock/eloquent-sluggable' => array(
            'pretty_version' => '11.0.1',
            'version' => '11.0.1.0',
            'reference' => 'a4281cf0284a21efc1031a065b112ddd6c826eea',
            'type' => 'library',
            'install_path' => __DIR__ . '/../cviebrock/eloquent-sluggable',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'davedevelopment/hamcrest-php' => array(
            'dev_requirement' => true,
            'replaced' => array(
                0 => '*',
            ),
        ),
        'dflydev/dot-access-data' => array(
            'pretty_version' => 'v3.0.3',
            'version' => '3.0.3.0',
            'reference' => 'a23a2bf4f31d3518f3ecb38660c95715dfead60f',
            'type' => 'library',
            'install_path' => __DIR__ . '/../dflydev/dot-access-data',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'doctrine/inflector' => array(
            'pretty_version' => '2.0.10',
            'version' => '2.0.10.0',
            'reference' => '5817d0659c5b50c9b950feb9af7b9668e2c436bc',
            'type' => 'library',
            'install_path' => __DIR__ . '/../doctrine/inflector',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'doctrine/lexer' => array(
            'pretty_version' => '3.0.1',
            'version' => '3.0.1.0',
            'reference' => '31ad66abc0fc9e1a1f2d9bc6a42668d2fbbcd6dd',
            'type' => 'library',
            'install_path' => __DIR__ . '/../doctrine/lexer',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'dragonmantank/cron-expression' => array(
            'pretty_version' => 'v3.4.0',
            'version' => '3.4.0.0',
            'reference' => '8c784d071debd117328803d86b2097615b457500',
            'type' => 'library',
            'install_path' => __DIR__ . '/../dragonmantank/cron-expression',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'egulias/email-validator' => array(
            'pretty_version' => '4.0.3',
            'version' => '4.0.3.0',
            'reference' => 'b115554301161fa21467629f1e1391c1936de517',
            'type' => 'library',
            'install_path' => __DIR__ . '/../egulias/email-validator',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'ezyang/htmlpurifier' => array(
            'pretty_version' => 'v4.18.0',
            'version' => '4.18.0.0',
            'reference' => 'cb56001e54359df7ae76dc522d08845dc741621b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../ezyang/htmlpurifier',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'fakerphp/faker' => array(
            'pretty_version' => 'v1.24.1',
            'version' => '1.24.1.0',
            'reference' => 'e0ee18eb1e6dc3cda3ce9fd97e5a0689a88a64b5',
            'type' => 'library',
            'install_path' => __DIR__ . '/../fakerphp/faker',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'filp/whoops' => array(
            'pretty_version' => '2.17.0',
            'version' => '2.17.0.0',
            'reference' => '075bc0c26631110584175de6523ab3f1652eb28e',
            'type' => 'library',
            'install_path' => __DIR__ . '/../filp/whoops',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'firebase/php-jwt' => array(
            'pretty_version' => 'v6.11.0',
            'version' => '6.11.0.0',
            'reference' => '8f718f4dfc9c5d5f0c994cdfd103921b43592712',
            'type' => 'library',
            'install_path' => __DIR__ . '/../firebase/php-jwt',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'fruitcake/php-cors' => array(
            'pretty_version' => 'v1.3.0',
            'version' => '1.3.0.0',
            'reference' => '3d158f36e7875e2f040f37bc0573956240a5a38b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../fruitcake/php-cors',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'graham-campbell/manager' => array(
            'pretty_version' => 'v5.1.1',
            'version' => '5.1.1.0',
            'reference' => '176b211828cdeabb39d97677fb14057c7cbf36a6',
            'type' => 'library',
            'install_path' => __DIR__ . '/../graham-campbell/manager',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'graham-campbell/result-type' => array(
            'pretty_version' => 'v1.1.3',
            'version' => '1.1.3.0',
            'reference' => '3ba905c11371512af9d9bdd27d99b782216b6945',
            'type' => 'library',
            'install_path' => __DIR__ . '/../graham-campbell/result-type',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'guzzlehttp/guzzle' => array(
            'pretty_version' => '7.9.2',
            'version' => '7.9.2.0',
            'reference' => 'd281ed313b989f213357e3be1a179f02196ac99b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../guzzlehttp/guzzle',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'guzzlehttp/promises' => array(
            'pretty_version' => '2.0.4',
            'version' => '2.0.4.0',
            'reference' => 'f9c436286ab2892c7db7be8c8da4ef61ccf7b455',
            'type' => 'library',
            'install_path' => __DIR__ . '/../guzzlehttp/promises',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'guzzlehttp/psr7' => array(
            'pretty_version' => '2.7.0',
            'version' => '2.7.0.0',
            'reference' => 'a70f5c95fb43bc83f07c9c948baa0dc1829bf201',
            'type' => 'library',
            'install_path' => __DIR__ . '/../guzzlehttp/psr7',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'guzzlehttp/uri-template' => array(
            'pretty_version' => 'v1.0.4',
            'version' => '1.0.4.0',
            'reference' => '30e286560c137526eccd4ce21b2de477ab0676d2',
            'type' => 'library',
            'install_path' => __DIR__ . '/../guzzlehttp/uri-template',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'hamcrest/hamcrest-php' => array(
            'pretty_version' => 'v2.0.1',
            'version' => '2.0.1.0',
            'reference' => '8c3d0a3f6af734494ad8f6fbbee0ba92422859f3',
            'type' => 'library',
            'install_path' => __DIR__ . '/../hamcrest/hamcrest-php',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'hashids/hashids' => array(
            'pretty_version' => '5.0.2',
            'version' => '5.0.2.0',
            'reference' => '197171016b77ddf14e259e186559152eb3f8cf33',
            'type' => 'library',
            'install_path' => __DIR__ . '/../hashids/hashids',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'illuminate/auth' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v11.43.2',
            ),
        ),
        'illuminate/broadcasting' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v11.43.2',
            ),
        ),
        'illuminate/bus' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v11.43.2',
            ),
        ),
        'illuminate/cache' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v11.43.2',
            ),
        ),
        'illuminate/collections' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v11.43.2',
            ),
        ),
        'illuminate/concurrency' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v11.43.2',
            ),
        ),
        'illuminate/conditionable' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v11.43.2',
            ),
        ),
        'illuminate/config' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v11.43.2',
            ),
        ),
        'illuminate/console' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v11.43.2',
            ),
        ),
        'illuminate/container' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v11.43.2',
            ),
        ),
        'illuminate/contracts' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v11.43.2',
            ),
        ),
        'illuminate/cookie' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v11.43.2',
            ),
        ),
        'illuminate/database' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v11.43.2',
            ),
        ),
        'illuminate/encryption' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v11.43.2',
            ),
        ),
        'illuminate/events' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v11.43.2',
            ),
        ),
        'illuminate/filesystem' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v11.43.2',
            ),
        ),
        'illuminate/hashing' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v11.43.2',
            ),
        ),
        'illuminate/http' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v11.43.2',
            ),
        ),
        'illuminate/log' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v11.43.2',
            ),
        ),
        'illuminate/macroable' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v11.43.2',
            ),
        ),
        'illuminate/mail' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v11.43.2',
            ),
        ),
        'illuminate/notifications' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v11.43.2',
            ),
        ),
        'illuminate/pagination' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v11.43.2',
            ),
        ),
        'illuminate/pipeline' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v11.43.2',
            ),
        ),
        'illuminate/process' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v11.43.2',
            ),
        ),
        'illuminate/queue' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v11.43.2',
            ),
        ),
        'illuminate/redis' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v11.43.2',
            ),
        ),
        'illuminate/routing' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v11.43.2',
            ),
        ),
        'illuminate/session' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v11.43.2',
            ),
        ),
        'illuminate/support' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v11.43.2',
            ),
        ),
        'illuminate/testing' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v11.43.2',
            ),
        ),
        'illuminate/translation' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v11.43.2',
            ),
        ),
        'illuminate/validation' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v11.43.2',
            ),
        ),
        'illuminate/view' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v11.43.2',
            ),
        ),
        'intervention/gif' => array(
            'pretty_version' => '4.2.1',
            'version' => '4.2.1.0',
            'reference' => '6addac2c68b4bc0e37d0d3ccedda57eb84729c49',
            'type' => 'library',
            'install_path' => __DIR__ . '/../intervention/gif',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'intervention/image' => array(
            'pretty_version' => '3.11.1',
            'version' => '3.11.1.0',
            'reference' => '0f87254688e480fbb521e2a1ac6c11c784ca41af',
            'type' => 'library',
            'install_path' => __DIR__ . '/../intervention/image',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'intervention/image-laravel' => array(
            'pretty_version' => '1.2.0',
            'version' => '1.2.0.0',
            'reference' => 'd30b62fea3c49896dbf26ea7799e7d718e779310',
            'type' => 'library',
            'install_path' => __DIR__ . '/../intervention/image-laravel',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'irazasyed/telegram-bot-sdk' => array(
            'pretty_version' => 'v3.14.0',
            'version' => '3.14.0.0',
            'reference' => 'c72ef585556578105c4d5cc56324575ef3677fd2',
            'type' => 'library',
            'install_path' => __DIR__ . '/../irazasyed/telegram-bot-sdk',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'kodova/hamcrest-php' => array(
            'dev_requirement' => true,
            'replaced' => array(
                0 => '*',
            ),
        ),
        'laravel/framework' => array(
            'pretty_version' => 'v11.43.2',
            'version' => '11.43.2.0',
            'reference' => '99d1573698abc42222f04d25fcd5b213d0eedf21',
            'type' => 'library',
            'install_path' => __DIR__ . '/../laravel/framework',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'laravel/laravel' => array(
            'pretty_version' => 'dev-master',
            'version' => 'dev-master',
            'reference' => 'd2570354de71ac4e9eafd9008435219168dfa3b8',
            'type' => 'project',
            'install_path' => __DIR__ . '/../../',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'laravel/pint' => array(
            'pretty_version' => 'v1.21.0',
            'version' => '1.21.0.0',
            'reference' => '531fa0871fbde719c51b12afa3a443b8f4e4b425',
            'type' => 'project',
            'install_path' => __DIR__ . '/../laravel/pint',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'laravel/prompts' => array(
            'pretty_version' => 'v0.3.5',
            'version' => '0.3.5.0',
            'reference' => '57b8f7efe40333cdb925700891c7d7465325d3b1',
            'type' => 'library',
            'install_path' => __DIR__ . '/../laravel/prompts',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'laravel/sail' => array(
            'pretty_version' => 'v1.41.0',
            'version' => '1.41.0.0',
            'reference' => 'fe1a4ada0abb5e4bd99eb4e4b0d87906c00cdeec',
            'type' => 'library',
            'install_path' => __DIR__ . '/../laravel/sail',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'laravel/sanctum' => array(
            'pretty_version' => 'v4.0.8',
            'version' => '4.0.8.0',
            'reference' => 'ec1dd9ddb2ab370f79dfe724a101856e0963f43c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../laravel/sanctum',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'laravel/serializable-closure' => array(
            'pretty_version' => 'v2.0.3',
            'version' => '*******',
            'reference' => 'f379c13663245f7aa4512a7869f62eb14095f23f',
            'type' => 'library',
            'install_path' => __DIR__ . '/../laravel/serializable-closure',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'laravel/socialite' => array(
            'pretty_version' => 'v5.18.0',
            'version' => '5.18.0.0',
            'reference' => '7809dc71250e074cd42970f0f803f2cddc04c5de',
            'type' => 'library',
            'install_path' => __DIR__ . '/../laravel/socialite',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'laravel/tinker' => array(
            'pretty_version' => 'v2.10.1',
            'version' => '2.10.1.0',
            'reference' => '22177cc71807d38f2810c6204d8f7183d88a57d3',
            'type' => 'library',
            'install_path' => __DIR__ . '/../laravel/tinker',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'laravel/ui' => array(
            'pretty_version' => 'v4.6.1',
            'version' => '4.6.1.0',
            'reference' => '7d6ffa38d79f19c9b3e70a751a9af845e8f41d88',
            'type' => 'library',
            'install_path' => __DIR__ . '/../laravel/ui',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'league/commonmark' => array(
            'pretty_version' => '2.6.1',
            'version' => '2.6.1.0',
            'reference' => 'd990688c91cedfb69753ffc2512727ec646df2ad',
            'type' => 'library',
            'install_path' => __DIR__ . '/../league/commonmark',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'league/config' => array(
            'pretty_version' => 'v1.2.0',
            'version' => '1.2.0.0',
            'reference' => '754b3604fb2984c71f4af4a9cbe7b57f346ec1f3',
            'type' => 'library',
            'install_path' => __DIR__ . '/../league/config',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'league/event' => array(
            'pretty_version' => '3.0.3',
            'version' => '3.0.3.0',
            'reference' => 'ec38ff7ea10cad7d99a79ac937fbcffb9334c210',
            'type' => 'library',
            'install_path' => __DIR__ . '/../league/event',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'league/flysystem' => array(
            'pretty_version' => '3.29.1',
            'version' => '3.29.1.0',
            'reference' => 'edc1bb7c86fab0776c3287dbd19b5fa278347319',
            'type' => 'library',
            'install_path' => __DIR__ . '/../league/flysystem',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'league/flysystem-local' => array(
            'pretty_version' => '3.29.0',
            'version' => '3.29.0.0',
            'reference' => 'e0e8d52ce4b2ed154148453d321e97c8e931bd27',
            'type' => 'library',
            'install_path' => __DIR__ . '/../league/flysystem-local',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'league/mime-type-detection' => array(
            'pretty_version' => '1.16.0',
            'version' => '1.16.0.0',
            'reference' => '2d6702ff215bf922936ccc1ad31007edc76451b9',
            'type' => 'library',
            'install_path' => __DIR__ . '/../league/mime-type-detection',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'league/oauth1-client' => array(
            'pretty_version' => 'v1.11.0',
            'version' => '1.11.0.0',
            'reference' => 'f9c94b088837eb1aae1ad7c4f23eb65cc6993055',
            'type' => 'library',
            'install_path' => __DIR__ . '/../league/oauth1-client',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'league/uri' => array(
            'pretty_version' => '7.5.1',
            'version' => '7.5.1.0',
            'reference' => '81fb5145d2644324614cc532b28efd0215bda430',
            'type' => 'library',
            'install_path' => __DIR__ . '/../league/uri',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'league/uri-interfaces' => array(
            'pretty_version' => '7.5.0',
            'version' => '7.5.0.0',
            'reference' => '08cfc6c4f3d811584fb09c37e2849e6a7f9b0742',
            'type' => 'library',
            'install_path' => __DIR__ . '/../league/uri-interfaces',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'mcamara/laravel-localization' => array(
            'pretty_version' => 'v2.2.1',
            'version' => '2.2.1.0',
            'reference' => 'a19ddcc634dff6b06ae35ecb7f06b2a0444e9807',
            'type' => 'library',
            'install_path' => __DIR__ . '/../mcamara/laravel-localization',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'mockery/mockery' => array(
            'pretty_version' => '1.6.12',
            'version' => '1.6.12.0',
            'reference' => '1f4efdd7d3beafe9807b08156dfcb176d18f1699',
            'type' => 'library',
            'install_path' => __DIR__ . '/../mockery/mockery',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'monolog/monolog' => array(
            'pretty_version' => '3.8.1',
            'version' => '3.8.1.0',
            'reference' => 'aef6ee73a77a66e404dd6540934a9ef1b3c855b4',
            'type' => 'library',
            'install_path' => __DIR__ . '/../monolog/monolog',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'mtdowling/cron-expression' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '^1.0',
            ),
        ),
        'myclabs/deep-copy' => array(
            'pretty_version' => '1.13.0',
            'version' => '1.13.0.0',
            'reference' => '024473a478be9df5fdaca2c793f2232fe788e414',
            'type' => 'library',
            'install_path' => __DIR__ . '/../myclabs/deep-copy',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'nesbot/carbon' => array(
            'pretty_version' => '2.73.0',
            'version' => '2.73.0.0',
            'reference' => '9228ce90e1035ff2f0db84b40ec2e023ed802075',
            'type' => 'library',
            'install_path' => __DIR__ . '/../nesbot/carbon',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'nette/schema' => array(
            'pretty_version' => 'v1.3.2',
            'version' => '1.3.2.0',
            'reference' => 'da801d52f0354f70a638673c4a0f04e16529431d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../nette/schema',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'nette/utils' => array(
            'pretty_version' => 'v4.0.5',
            'version' => '4.0.5.0',
            'reference' => '736c567e257dbe0fcf6ce81b4d6dbe05c6899f96',
            'type' => 'library',
            'install_path' => __DIR__ . '/../nette/utils',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'nikic/php-parser' => array(
            'pretty_version' => 'v5.4.0',
            'version' => '5.4.0.0',
            'reference' => '447a020a1f875a434d62f2a401f53b82a396e494',
            'type' => 'library',
            'install_path' => __DIR__ . '/../nikic/php-parser',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'nunomaduro/collision' => array(
            'pretty_version' => 'v8.6.1',
            'version' => '8.6.1.0',
            'reference' => '86f003c132143d5a2ab214e19933946409e0cae7',
            'type' => 'library',
            'install_path' => __DIR__ . '/../nunomaduro/collision',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'nunomaduro/termwind' => array(
            'pretty_version' => 'v2.3.0',
            'version' => '*******',
            'reference' => '52915afe6a1044e8b9cee1bcff836fb63acf9cda',
            'type' => 'library',
            'install_path' => __DIR__ . '/../nunomaduro/termwind',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'paragonie/constant_time_encoding' => array(
            'pretty_version' => 'v3.0.0',
            'version' => '3.0.0.0',
            'reference' => 'df1e7fde177501eee2037dd159cf04f5f301a512',
            'type' => 'library',
            'install_path' => __DIR__ . '/../paragonie/constant_time_encoding',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'paragonie/random_compat' => array(
            'pretty_version' => 'v9.99.100',
            'version' => '9.99.100.0',
            'reference' => '996434e5492cb4c3edcb9168db6fbb1359ef965a',
            'type' => 'library',
            'install_path' => __DIR__ . '/../paragonie/random_compat',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'phar-io/manifest' => array(
            'pretty_version' => '2.0.4',
            'version' => '2.0.4.0',
            'reference' => '54750ef60c58e43759730615a392c31c80e23176',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phar-io/manifest',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'phar-io/version' => array(
            'pretty_version' => '3.2.1',
            'version' => '3.2.1.0',
            'reference' => '4f7fd7836c6f332bb2933569e566a0d6c4cbed74',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phar-io/version',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'php-debugbar/php-debugbar' => array(
            'pretty_version' => 'v2.1.5',
            'version' => '2.1.5.0',
            'reference' => '3f589bbbaed53039d9699702c2908148647c27a1',
            'type' => 'library',
            'install_path' => __DIR__ . '/../php-debugbar/php-debugbar',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'php-flasher/flasher' => array(
            'pretty_version' => 'v1.15.14',
            'version' => '1.15.14.0',
            'reference' => '33ae74e73f62814fff4e78e78f912d9b6ddf82d0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../php-flasher/flasher',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'php-flasher/flasher-laravel' => array(
            'pretty_version' => 'v1.15.14',
            'version' => '1.15.14.0',
            'reference' => 'c2777483fd7074087c16f861ce2191a95088e7c6',
            'type' => 'library',
            'install_path' => __DIR__ . '/../php-flasher/flasher-laravel',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'php-flasher/flasher-notyf' => array(
            'pretty_version' => 'v1.15.14',
            'version' => '1.15.14.0',
            'reference' => 'f454aea626cdccf0191ae914e7f74fb4aa3cabdd',
            'type' => 'library',
            'install_path' => __DIR__ . '/../php-flasher/flasher-notyf',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'php-flasher/flasher-notyf-laravel' => array(
            'pretty_version' => 'v1.15.0',
            'version' => '1.15.0.0',
            'reference' => '861032e25bb6eb74e07e9a5304cd905c55e34759',
            'type' => 'library',
            'install_path' => __DIR__ . '/../php-flasher/flasher-notyf-laravel',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'php-mime-mail-parser/php-mime-mail-parser' => array(
            'pretty_version' => 'v1.0.0',
            'version' => '1.0.0.0',
            'reference' => 'bd5b876742301b0e54c06527f807378789cfc5bb',
            'type' => 'library',
            'install_path' => __DIR__ . '/../php-mime-mail-parser/php-mime-mail-parser',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'phpoption/phpoption' => array(
            'pretty_version' => '1.9.3',
            'version' => '1.9.3.0',
            'reference' => 'e3fac8b24f56113f7cb96af14958c0dd16330f54',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpoption/phpoption',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'phpseclib/phpseclib' => array(
            'pretty_version' => '3.0.43',
            'version' => '3.0.43.0',
            'reference' => '709ec107af3cb2f385b9617be72af8cf62441d02',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpseclib/phpseclib',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'phpunit/php-code-coverage' => array(
            'pretty_version' => '11.0.8',
            'version' => '11.0.8.0',
            'reference' => '418c59fd080954f8c4aa5631d9502ecda2387118',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpunit/php-code-coverage',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'phpunit/php-file-iterator' => array(
            'pretty_version' => '5.1.0',
            'version' => '5.1.0.0',
            'reference' => '118cfaaa8bc5aef3287bf315b6060b1174754af6',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpunit/php-file-iterator',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'phpunit/php-invoker' => array(
            'pretty_version' => '5.0.1',
            'version' => '5.0.1.0',
            'reference' => 'c1ca3814734c07492b3d4c5f794f4b0995333da2',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpunit/php-invoker',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'phpunit/php-text-template' => array(
            'pretty_version' => '4.0.1',
            'version' => '4.0.1.0',
            'reference' => '3e0404dc6b300e6bf56415467ebcb3fe4f33e964',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpunit/php-text-template',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'phpunit/php-timer' => array(
            'pretty_version' => '7.0.1',
            'version' => '7.0.1.0',
            'reference' => '3b415def83fbcb41f991d9ebf16ae4ad8b7837b3',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpunit/php-timer',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'phpunit/phpunit' => array(
            'pretty_version' => '11.5.9',
            'version' => '11.5.9.0',
            'reference' => 'c91c830e7108a81e5845aeb6ba8fe3c1a4351c0b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpunit/phpunit',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'psr/clock' => array(
            'pretty_version' => '1.0.0',
            'version' => '1.0.0.0',
            'reference' => 'e41a24703d4560fd0acb709162f73b8adfc3aa0d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/clock',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/clock-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'psr/container' => array(
            'pretty_version' => '2.0.2',
            'version' => '2.0.2.0',
            'reference' => 'c71ecc56dfe541dbd90c5360474fbc405f8d5963',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/container',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/container-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.1|2.0',
            ),
        ),
        'psr/event-dispatcher' => array(
            'pretty_version' => '1.0.0',
            'version' => '1.0.0.0',
            'reference' => 'dbefd12671e8a14ec7f180cab83036ed26714bb0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/event-dispatcher',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/event-dispatcher-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'psr/http-client' => array(
            'pretty_version' => '1.0.3',
            'version' => '1.0.3.0',
            'reference' => 'bb5906edc1c324c9a05aa0873d40117941e5fa90',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-client',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/http-client-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'psr/http-factory' => array(
            'pretty_version' => '1.1.0',
            'version' => '*******',
            'reference' => '2b4765fddfe3b508ac62f829e852b1501d3f6e8a',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-factory',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/http-factory-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'psr/http-message' => array(
            'pretty_version' => '2.0',
            'version' => '2.0.0.0',
            'reference' => '402d35bcb92c70c026d1a6a9883f06b2ead23d71',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-message',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/http-message-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'psr/log' => array(
            'pretty_version' => '3.0.2',
            'version' => '3.0.2.0',
            'reference' => 'f16e1d5863e37f8d8c2a01719f5b34baa2b714d3',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/log',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/log-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0|2.0|3.0',
                1 => '3.0.0',
            ),
        ),
        'psr/simple-cache' => array(
            'pretty_version' => '3.0.0',
            'version' => '3.0.0.0',
            'reference' => '764e0b3939f5ca87cb904f570ef9be2d78a07865',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/simple-cache',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/simple-cache-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0|2.0|3.0',
            ),
        ),
        'psy/psysh' => array(
            'pretty_version' => 'v0.12.7',
            'version' => '0.12.7.0',
            'reference' => 'd73fa3c74918ef4522bb8a3bf9cab39161c4b57c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psy/psysh',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'ralouphie/getallheaders' => array(
            'pretty_version' => '3.0.3',
            'version' => '3.0.3.0',
            'reference' => '120b605dfeb996808c31b6477290a714d356e822',
            'type' => 'library',
            'install_path' => __DIR__ . '/../ralouphie/getallheaders',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'ramsey/collection' => array(
            'pretty_version' => '2.0.0',
            'version' => '2.0.0.0',
            'reference' => 'a4b48764bfbb8f3a6a4d1aeb1a35bb5e9ecac4a5',
            'type' => 'library',
            'install_path' => __DIR__ . '/../ramsey/collection',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'ramsey/uuid' => array(
            'pretty_version' => '4.7.6',
            'version' => '4.7.6.0',
            'reference' => '91039bc1faa45ba123c4328958e620d382ec7088',
            'type' => 'library',
            'install_path' => __DIR__ . '/../ramsey/uuid',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'rhumsaa/uuid' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '4.7.6',
            ),
        ),
        'sebastian/cli-parser' => array(
            'pretty_version' => '3.0.2',
            'version' => '3.0.2.0',
            'reference' => '15c5dd40dc4f38794d383bb95465193f5e0ae180',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/cli-parser',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/code-unit' => array(
            'pretty_version' => '3.0.2',
            'version' => '3.0.2.0',
            'reference' => 'ee88b0cdbe74cf8dd3b54940ff17643c0d6543ca',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/code-unit',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/code-unit-reverse-lookup' => array(
            'pretty_version' => '4.0.1',
            'version' => '4.0.1.0',
            'reference' => '183a9b2632194febd219bb9246eee421dad8d45e',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/code-unit-reverse-lookup',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/comparator' => array(
            'pretty_version' => '6.3.0',
            'version' => '6.3.0.0',
            'reference' => 'd4e47a769525c4dd38cea90e5dcd435ddbbc7115',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/comparator',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/complexity' => array(
            'pretty_version' => '4.0.1',
            'version' => '4.0.1.0',
            'reference' => 'ee41d384ab1906c68852636b6de493846e13e5a0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/complexity',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/diff' => array(
            'pretty_version' => '6.0.2',
            'version' => '6.0.2.0',
            'reference' => 'b4ccd857127db5d41a5b676f24b51371d76d8544',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/diff',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/environment' => array(
            'pretty_version' => '7.2.0',
            'version' => '7.2.0.0',
            'reference' => '855f3ae0ab316bbafe1ba4e16e9f3c078d24a0c5',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/environment',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/exporter' => array(
            'pretty_version' => '6.3.0',
            'version' => '6.3.0.0',
            'reference' => '3473f61172093b2da7de1fb5782e1f24cc036dc3',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/exporter',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/global-state' => array(
            'pretty_version' => '7.0.2',
            'version' => '7.0.2.0',
            'reference' => '3be331570a721f9a4b5917f4209773de17f747d7',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/global-state',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/lines-of-code' => array(
            'pretty_version' => '3.0.1',
            'version' => '3.0.1.0',
            'reference' => 'd36ad0d782e5756913e42ad87cb2890f4ffe467a',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/lines-of-code',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/object-enumerator' => array(
            'pretty_version' => '6.0.1',
            'version' => '6.0.1.0',
            'reference' => 'f5b498e631a74204185071eb41f33f38d64608aa',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/object-enumerator',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/object-reflector' => array(
            'pretty_version' => '4.0.1',
            'version' => '4.0.1.0',
            'reference' => '6e1a43b411b2ad34146dee7524cb13a068bb35f9',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/object-reflector',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/recursion-context' => array(
            'pretty_version' => '6.0.2',
            'version' => '6.0.2.0',
            'reference' => '694d156164372abbd149a4b85ccda2e4670c0e16',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/recursion-context',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/type' => array(
            'pretty_version' => '5.1.0',
            'version' => '5.1.0.0',
            'reference' => '461b9c5da241511a2a0e8f240814fb23ce5c0aac',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/type',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/version' => array(
            'pretty_version' => '5.0.2',
            'version' => '5.0.2.0',
            'reference' => 'c687e3387b99f5b03b6caa64c74b63e2936ff874',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/version',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'spatie/once' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '*',
            ),
        ),
        'staabm/side-effects-detector' => array(
            'pretty_version' => '1.0.5',
            'version' => '1.0.5.0',
            'reference' => 'd8334211a140ce329c13726d4a715adbddd0a163',
            'type' => 'library',
            'install_path' => __DIR__ . '/../staabm/side-effects-detector',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'symfony/console' => array(
            'pretty_version' => 'v7.2.1',
            'version' => '7.2.1.0',
            'reference' => 'fefcc18c0f5d0efe3ab3152f15857298868dc2c3',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/console',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/css-selector' => array(
            'pretty_version' => 'v7.2.0',
            'version' => '7.2.0.0',
            'reference' => '601a5ce9aaad7bf10797e3663faefce9e26c24e2',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/css-selector',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/deprecation-contracts' => array(
            'pretty_version' => 'v3.5.1',
            'version' => '3.5.1.0',
            'reference' => '74c71c939a79f7d5bf3c1ce9f5ea37ba0114c6f6',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/deprecation-contracts',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/error-handler' => array(
            'pretty_version' => 'v7.2.3',
            'version' => '7.2.3.0',
            'reference' => '959a74d044a6db21f4caa6d695648dcb5584cb49',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/error-handler',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/event-dispatcher' => array(
            'pretty_version' => 'v7.2.0',
            'version' => '7.2.0.0',
            'reference' => '910c5db85a5356d0fea57680defec4e99eb9c8c1',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/event-dispatcher',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/event-dispatcher-contracts' => array(
            'pretty_version' => 'v3.5.1',
            'version' => '3.5.1.0',
            'reference' => '7642f5e970b672283b7823222ae8ef8bbc160b9f',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/event-dispatcher-contracts',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/event-dispatcher-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '2.0|3.0',
            ),
        ),
        'symfony/finder' => array(
            'pretty_version' => 'v7.2.2',
            'version' => '7.2.2.0',
            'reference' => '87a71856f2f56e4100373e92529eed3171695cfb',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/finder',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/http-foundation' => array(
            'pretty_version' => 'v7.2.3',
            'version' => '7.2.3.0',
            'reference' => 'ee1b504b8926198be89d05e5b6fc4c3810c090f0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/http-foundation',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/http-kernel' => array(
            'pretty_version' => 'v7.2.3',
            'version' => '7.2.3.0',
            'reference' => 'caae9807f8e25a9b43ce8cc6fafab6cf91f0cc9b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/http-kernel',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/mailer' => array(
            'pretty_version' => 'v7.2.3',
            'version' => '7.2.3.0',
            'reference' => 'f3871b182c44997cf039f3b462af4a48fb85f9d3',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/mailer',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/mime' => array(
            'pretty_version' => 'v7.2.3',
            'version' => '7.2.3.0',
            'reference' => '2fc3b4bd67e4747e45195bc4c98bea4628476204',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/mime',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-ctype' => array(
            'pretty_version' => 'v1.31.0',
            'version' => '1.31.0.0',
            'reference' => 'a3cc8b044a6ea513310cbd48ef7333b384945638',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-ctype',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-intl-grapheme' => array(
            'pretty_version' => 'v1.31.0',
            'version' => '1.31.0.0',
            'reference' => 'b9123926e3b7bc2f98c02ad54f6a4b02b91a8abe',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-intl-grapheme',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-intl-idn' => array(
            'pretty_version' => 'v1.31.0',
            'version' => '1.31.0.0',
            'reference' => 'c36586dcf89a12315939e00ec9b4474adcb1d773',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-intl-idn',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-intl-normalizer' => array(
            'pretty_version' => 'v1.31.0',
            'version' => '1.31.0.0',
            'reference' => '3833d7255cc303546435cb650316bff708a1c75c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-intl-normalizer',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-mbstring' => array(
            'pretty_version' => 'v1.31.0',
            'version' => '1.31.0.0',
            'reference' => '85181ba99b2345b0ef10ce42ecac37612d9fd341',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-mbstring',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-php80' => array(
            'pretty_version' => 'v1.31.0',
            'version' => '1.31.0.0',
            'reference' => '60328e362d4c2c802a54fcbf04f9d3fb892b4cf8',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-php80',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-php83' => array(
            'pretty_version' => 'v1.31.0',
            'version' => '1.31.0.0',
            'reference' => '2fb86d65e2d424369ad2905e83b236a8805ba491',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-php83',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-uuid' => array(
            'pretty_version' => 'v1.31.0',
            'version' => '1.31.0.0',
            'reference' => '21533be36c24be3f4b1669c4725c7d1d2bab4ae2',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-uuid',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/process' => array(
            'pretty_version' => 'v7.2.0',
            'version' => '7.2.0.0',
            'reference' => 'd34b22ba9390ec19d2dd966c40aa9e8462f27a7e',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/process',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/routing' => array(
            'pretty_version' => 'v7.2.3',
            'version' => '7.2.3.0',
            'reference' => 'ee9a67edc6baa33e5fae662f94f91fd262930996',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/routing',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/service-contracts' => array(
            'pretty_version' => 'v3.5.1',
            'version' => '3.5.1.0',
            'reference' => 'e53260aabf78fb3d63f8d79d69ece59f80d5eda0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/service-contracts',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/string' => array(
            'pretty_version' => 'v7.2.0',
            'version' => '7.2.0.0',
            'reference' => '446e0d146f991dde3e73f45f2c97a9faad773c82',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/string',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/translation' => array(
            'pretty_version' => 'v6.4.13',
            'version' => '6.4.13.0',
            'reference' => 'bee9bfabfa8b4045a66bf82520e492cddbaffa66',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/translation',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/translation-contracts' => array(
            'pretty_version' => 'v3.5.1',
            'version' => '3.5.1.0',
            'reference' => '4667ff3bd513750603a09c8dedbea942487fb07c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/translation-contracts',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/translation-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '2.3|3.0',
            ),
        ),
        'symfony/uid' => array(
            'pretty_version' => 'v7.2.0',
            'version' => '7.2.0.0',
            'reference' => '2d294d0c48df244c71c105a169d0190bfb080426',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/uid',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/var-dumper' => array(
            'pretty_version' => 'v7.2.3',
            'version' => '7.2.3.0',
            'reference' => '82b478c69745d8878eb60f9a049a4d584996f73a',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/var-dumper',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/yaml' => array(
            'pretty_version' => 'v7.2.3',
            'version' => '7.2.3.0',
            'reference' => 'ac238f173df0c9c1120f862d0f599e17535a87ec',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/yaml',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'teampanfu/laravel-hcaptcha' => array(
            'pretty_version' => 'v1.1.2',
            'version' => '1.1.2.0',
            'reference' => '0eff9395c9bc8ebfeea73d1fba0f88e6c518e499',
            'type' => 'library',
            'install_path' => __DIR__ . '/../teampanfu/laravel-hcaptcha',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'theseer/tokenizer' => array(
            'pretty_version' => '1.2.3',
            'version' => '1.2.3.0',
            'reference' => '737eda637ed5e28c3413cb1ebe8bb52cbf1ca7a2',
            'type' => 'library',
            'install_path' => __DIR__ . '/../theseer/tokenizer',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'tijsverkoyen/css-to-inline-styles' => array(
            'pretty_version' => 'v2.3.0',
            'version' => '*******',
            'reference' => '0d72ac1c00084279c1816675284073c5a337c20d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../tijsverkoyen/css-to-inline-styles',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'trustip/trustip' => array(
            'pretty_version' => 'v1.1',
            'version' => '*******',
            'reference' => 'fd6a8d448d38ad8a1e3a1b9b87be2c9f5869c85e',
            'type' => 'library',
            'install_path' => __DIR__ . '/../trustip/trustip',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'vinkla/hashids' => array(
            'pretty_version' => '12.0.0',
            'version' => '********',
            'reference' => '71e4be8347d8c77dd728b61c1ff3b53cb4941ef1',
            'type' => 'library',
            'install_path' => __DIR__ . '/../vinkla/hashids',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'vlucas/phpdotenv' => array(
            'pretty_version' => 'v5.6.1',
            'version' => '*******',
            'reference' => 'a59a13791077fe3d44f90e7133eb68e7d22eaff2',
            'type' => 'library',
            'install_path' => __DIR__ . '/../vlucas/phpdotenv',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'voku/portable-ascii' => array(
            'pretty_version' => '2.0.3',
            'version' => '*******',
            'reference' => 'b1d923f88091c6bf09699efcd7c8a1b1bfd7351d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../voku/portable-ascii',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'webklex/laravel-imap' => array(
            'pretty_version' => '5.3.0',
            'version' => '5.3.0.0',
            'reference' => 'a424988a314c09f924390011a7baa8025efb9e14',
            'type' => 'library',
            'install_path' => __DIR__ . '/../webklex/laravel-imap',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'webklex/php-imap' => array(
            'pretty_version' => '5.5.0',
            'version' => '5.5.0.0',
            'reference' => '3c23c8f66b772ce8597772816e068326559e7e4b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../webklex/php-imap',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'webmozart/assert' => array(
            'pretty_version' => '1.11.0',
            'version' => '1.11.0.0',
            'reference' => '11cb2199493b2f8a3b53e7f19068fc6aac760991',
            'type' => 'library',
            'install_path' => __DIR__ . '/../webmozart/assert',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
    ),
);
