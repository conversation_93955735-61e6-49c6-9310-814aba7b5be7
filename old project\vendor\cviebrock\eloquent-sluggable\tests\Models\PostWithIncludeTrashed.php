<?php namespace C<PERSON><PERSON>ck\EloquentSluggable\Tests\Models;

/**
 * Class PostWithIncludeTrashed
 *
 * A test model that uses the Sluggable package and "includeTrashed",
 * but does not use <PERSON><PERSON>'s SoftDeleting trait.
 *
 * @package Cviebrock\EloquentSluggable\Tests\Models
 */
class PostWithIncludeTrashed extends Post
{

    public function sluggable(): array
    {
        return [
            'slug' => [
                'source' => 'title',
                'includeTrashed' => true
            ]
        ];
    }
}
