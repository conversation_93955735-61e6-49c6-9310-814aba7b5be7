<?php namespace C<PERSON><PERSON><PERSON>\EloquentSluggable\Tests\Models;

use <PERSON><PERSON><PERSON>ck\EloquentSluggable\SluggableObserver;

/**
 * Class PostWithIdSourceOnSaved
 *
 * A test model that uses the model's ID in the slug source
 * and the SluggableObserver::SAVED event listener.
 *
 * @package C<PERSON>brock\EloquentSluggable\Tests\Models
 */
class PostWithIdSourceOnSaved extends Post
{

    /**
     * @inheritDoc
     */
    public function sluggableEvent(): string
    {
        return SluggableObserver::SAVED;
    }

    /**
     * Return the sluggable configuration array for this model.
     *
     * @return array
     */
    public function sluggable(): array
    {
        return [
            'slug' => [
                'source' => ['title','id'],
                'onUpdate' => true,
            ],
        ];
    }
}
