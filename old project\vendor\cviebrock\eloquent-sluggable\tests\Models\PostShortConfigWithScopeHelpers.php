<?php namespace C<PERSON><PERSON>ck\EloquentSluggable\Tests\Models;

use C<PERSON>brock\EloquentSluggable\SluggableScopeHelpers;

/**
 * Class PostShortConfigWithScopeHelpers
 *
 * @package Cviebrock\EloquentSluggable\Tests\Models
 */
class PostShortConfigWithScopeHelpers extends Post
{

    use SluggableScopeHelpers;

    /**
     * Return the sluggable configuration array for this model.
     *
     * @return array
     */
    public function sluggable(): array
    {
        return [
            'slug_field'
        ];
    }
}
