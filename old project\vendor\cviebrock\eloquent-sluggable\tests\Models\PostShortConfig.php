<?php namespace C<PERSON><PERSON><PERSON>\EloquentSluggable\Tests\Models;

/**
 * Class PostShortConfig
 *
 * @package C<PERSON>brock\EloquentSluggable\Tests\Models
 */
class PostShortConfig extends Post
{

    /**
     * Return the sluggable configuration array for this model.
     *
     * @return array
     */
    public function sluggable(): array
    {
        return [
            'slug'
        ];
    }
}
