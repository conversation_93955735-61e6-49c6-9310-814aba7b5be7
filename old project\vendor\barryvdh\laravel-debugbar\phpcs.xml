<?xml version="1.0"?>
<ruleset
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:noNamespaceSchemaLocation="vendor/squizlabs/php_codesniffer/phpcs.xsd"
>
    <file>config</file>
    <file>src</file>
    <file>tests</file>

    <exclude-pattern>src/Resources/*</exclude-pattern>
    <exclude-pattern>*.blade.php</exclude-pattern>

    <arg value="p"/>

    <rule ref="PSR12" />
</ruleset>
