{"name": "artesaos/seotools", "description": "SEO <PERSON>ls for Laravel and Lumen", "keywords": ["laravel", "lumen", "seo", "seotools", "webmaster", "metatags", "opengraph", "json-ld"], "license": "MIT", "support": {"issues": "https://github.com/artesaos/seotools/issues", "source": "https://github.com/artesaos/seotools"}, "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "require": {"php": "^8.1", "ext-json": "*", "illuminate/config": "^10.0 || ^11.0", "illuminate/support": "^10.0 || ^11.0"}, "require-dev": {"orchestra/testbench": "^8.0 || ^9.0", "phpspec/phpspec": "~5.1.1 || ^6.0 || ^7.0", "phpunit/phpunit": "^9.0 || ^10.0"}, "autoload": {"psr-4": {"Artesaos\\SEOTools\\": "src/SEOTools/"}}, "autoload-dev": {"psr-4": {"Artesaos\\SEOTools\\Tests\\": "tests/SEOTools"}}, "config": {"preferred-install": "dist"}, "extra": {"laravel": {"providers": ["Artesaos\\SEOTools\\Providers\\SEOToolsServiceProvider"], "aliases": {"SEOMeta": "Artesaos\\SEOTools\\Facades\\SEOMeta", "OpenGraph": "Artesaos\\SEOTools\\Facades\\OpenGraph", "Twitter": "Artesaos\\SEOTools\\Facades\\TwitterCard", "JsonLd": "Artesaos\\SEOTools\\Facades\\JsonLd", "SEO": "Artesaos\\SEOTools\\Facades\\SEOTools"}}}, "minimum-stability": "dev", "prefer-stable": true}