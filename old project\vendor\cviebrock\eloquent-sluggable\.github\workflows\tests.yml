name: tests

on: [push, pull_request]

jobs:
  tests:
    runs-on: ubuntu-latest

    strategy:
      fail-fast: false
      matrix:
        php: [8.2, 8.3, 8.4]
        stability: [prefer-lowest, prefer-stable]

    name: PHP ${{ matrix.php }} - ${{ matrix.stability }}

    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: ${{ matrix.php }}
          tools: composer:v2, cs2pr
          coverage: none
      - name: Setup problem matchers for PHPUnit
        run: echo "::add-matcher::${{ runner.tool_cache }}/phpunit.json"
      - name: Get composer cache directory
        id: composer-cache
        run: echo "dir=$(composer config cache-files-dir)" >> "$GITHUB_OUTPUT"
      - name: Cache dependencies
        uses: actions/cache@v4
        with:
          path: ${{ steps.composer-cache.outputs.dir }}
          key: dependencies-${{ matrix.php }}-${{ matrix.stability }}-composer-${{ hashFiles('**/composer.lock') }}
          restore-keys: dependencies-${{ matrix.php }}-${{ matrix.stability }}-composer-
      - name: Install dependencies
        run: composer update --${{ matrix.stability }} --prefer-dist --no-interaction
#      - name: Check coding style
#        run: php-cs-fixer fix --dry-run --allow-risky=true --format=checkstyle | cs2pr
      - name: Configure matchers for PHPUnit
        uses: mheap/phpunit-matcher-action@v1
      - name: Run PHP tests via PHPUnit
        run: vendor/bin/pest --teamcity
